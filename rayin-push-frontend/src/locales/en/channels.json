{"title": "Notification Channels", "createChannel": "Create Channel", "editChannel": "Edit Channel", "deleteChannel": "Delete Channel", "channelName": "Channel Name", "channelType": "Channel Type", "channelDescription": "Channel Description", "wechatBot": "WeChat Bot", "feishuBot": "<PERSON><PERSON><PERSON>", "customWebhook": "Custom Webhook", "webhookUrl": "Webhook URL", "httpMethod": "HTTP Method", "requestHeaders": "Request Headers", "requestBody": "Request Body", "urlParameters": "URL Parameters", "builtinTemplate": "Built-in Template", "customTemplate": "Custom Template", "templateVariables": "Template Variables", "messageTemplate": "Message Template", "testChannel": "Test Channel", "testMessage": "Test Message", "sendTest": "Send Test", "testHistory": "Test History", "channelStatus": "Channel Status", "lastTestTime": "Last Test Time", "testSuccess": "Test Success", "testFailed": "Test Failed", "variableHelp": "Variable Help", "supportedVariables": "Supported Variables", "conditionalLogic": "Conditional Logic", "ifElseStatement": "if/else Statement", "arithmeticExpression": "Arithmetic Expression", "noChannelData": "No channel data available", "basicInfo": "Basic Information", "channelConfig": "Channel Configuration", "channelNameRequired": "Channel name is required", "channelTypeRequired": "Please select channel type", "webhookUrlRequired": "Webhook URL is required", "httpMethodRequired": "Please select HTTP method", "messageTemplateRequired": "Message template is required", "channelNamePlaceholder": "Enter channel name", "channelDescPlaceholder": "Enter channel description", "selectChannelTypePlaceholder": "Select channel type", "webhookUrlPlaceholder": "Enter webhook URL", "messageTemplatePlaceholder": "Enter message template", "headerKeyPlaceholder": "Header name", "headerValuePlaceholder": "Header value", "editChannelDesc": "Edit existing notification channel configuration", "createChannelDesc": "Create new notification channel configuration", "builtinTemplateDesc": "Use built-in template with common variable replacements", "variableHelpContent": "Supported variables: ${data} complete data, ${now} current time, ${title} message title, ${content} message content. Conditional statements: ${if condition}...${else}...${/if}. Arithmetic expressions: ${x1 + x2}", "completeData": "Complete data", "currentTime": "Current time", "messageTitle": "Message title", "messageContent": "Message content", "testing": "Testing...", "saving": "Saving...", "searchPlaceholder": "Search channel name or description", "testCompleted": "Test completed"}
{"welcome": "Welcome to <PERSON><PERSON>", "description": "Message push notification system", "dashboard": "Dashboard", "config": "Interface Config", "channels": "Notification Channels", "logs": "Request Logs", "limits": "Request Limits", "users": "User Management", "settings": "Settings", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "search": "Search", "create": "Create", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "failed": "Failed", "status": "Status", "actions": "Actions", "name": "Name", "time": "Time", "type": "Type", "management": "Management", "system": "System", "monitoring": "System Monitoring", "analytics": "Analytics", "webhooks": "Webhook Management", "database": "Database Status", "operate": "Operate", "updatedTime": "Updated Time", "createdTime": "Created Time", "totalRecords": "Total {{count}} records", "selectedItems": "{{count}} selected", "batchOperation": "Batch Operation", "batchEnable": "Batch Enable", "batchDisable": "<PERSON>ch Disable", "perPage": "Per page", "itemsPerPage": "items", "firstPage": "First", "lastPage": "Last", "previousPage": "Previous", "nextPage": "Next", "page": "Page {{current}} of {{total}}", "noData": "No data", "auto": "Auto", "showing": "Showing {{from}} to {{to}} of {{total}} entries", "rowsPerPage": "Rows per page", "active": "Active", "inactive": "Inactive", "enable": "Enable", "disable": "Disable", "allStatus": "All Status", "allTypes": "All Types", "testStatus": "Test Status", "add": "Add"}